import asyncio
import sys
import os
import re
import csv
import chardet
import subprocess
from urllib.parse import urlparse
from playwright.async_api import async_playwright, Page
import codecs

# 檢查並安裝Playwright瀏覽器
def install_playwright_browsers():
    try:
        subprocess.run(["playwright", "install", "chromium"], check=True)
        print("Playwright瀏覽器已成功安裝")
    except Exception as e:
        print(f"安裝Playwright瀏覽器失敗: {e}")
        print("請手動執行: playwright install chromium")
        sys.exit(1)

# 設定標準輸出為 UTF-8 編碼
if sys.stdout.encoding != 'UTF-8':
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)

def detect_encoding(file_path):
    """檢測文件編碼"""
    with open(file_path, 'rb') as f:
        rawdata = f.read(10000)
        result = chardet.detect(rawdata)
        return result['encoding']

def get_ip_from_csv(file_path):
    """
    從CSV文件讀取IP地址
    
    參數:
    - file_path: CSV文件的路徑
    
    返回值:
    - 從CSV文件中讀取到的IP地址列表
    """
    try:
        # 嘗試多種常見編碼
        encodings = ['utf-8-sig', 'big5', 'gbk', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    next(reader)  # 跳過標題行
                    ip_addresses = set()  # 使用集合去除重複
                    for row in reader:
                        if len(row) >= 2:  # 檢查是否有足夠的列
                            ip = row[1].strip()  # 假設IP在第二列
                            if re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', ip):
                                ip_addresses.add(ip)
                    if ip_addresses:  # 如果成功讀取到IP
                        return list(ip_addresses)
            except UnicodeDecodeError:
                continue
                
        print("無法識別文件編碼")
        return None
    except Exception as e:
        print(f"讀取CSV文件失敗: {e}")
        return None

async def capture_webpage_content(url: str, screenshot_xpath: str = None, wait_xpath: str = None):
    """
    導航到指定 URL，根據提供的 XPath 進行元素截圖，或截取整個頁面
    """
    # 確保使用正確的相對路徑
    screenshot_dir = os.path.join(os.path.dirname(__file__), "screenshots")
    os.makedirs(screenshot_dir, exist_ok=True)

    if screenshot_xpath:
        parsed_url = urlparse(url)
        path_segments = parsed_url.path.split('/')
        
        if path_segments and path_segments[-1]:
            filename_base = path_segments[-1]
        else:
            filename_base = parsed_url.netloc.replace('.', '_').replace(':', '_')
            
        filename_base = re.sub(r'[^\w\d\-_.]', '', filename_base)
        if not filename_base:
            filename_base = "untitled_page"
            
        output_filename = f"{filename_base}_element.png".replace('.html', '') 
    else:
        safe_url_name = re.sub(r'[^\w\d\-_.]', '_', url).replace('__', '_')
        output_filename = f"{safe_url_name}_fullpage.png".replace('.html', '')
        
    output_path = os.path.join(screenshot_dir, output_filename)

    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            page: Page = await browser.new_page()
            await page.route("**/*", lambda route: route.abort() if route.request.resource_type == "image" else route.continue_())

            try:
                await page.goto(url)

                # 判斷要等待哪個 XPath：優先使用 wait_xpath，如果沒有則使用 screenshot_xpath
                actual_wait_xpath = wait_xpath if wait_xpath else screenshot_xpath

                if actual_wait_xpath:
                    await page.wait_for_selector(f"xpath={actual_wait_xpath}", state="visible", timeout=30000)
                else:
                    await page.wait_for_load_state("networkidle")
                
                await page.evaluate('''() => {
                    const element = document.evaluate('//*[@id="fs-sticky-footer"]', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (element) {
                        element.style.display = 'none';
                    }
                }''')
                
                if screenshot_xpath:
                    element_to_capture = page.locator(f"xpath={screenshot_xpath}")
                    await element_to_capture.screenshot(path=output_path)
                    print(f"成功,{os.path.abspath(output_path)}")
                else:
                    await page.screenshot(path=output_path, full_page=True)
                    print(f"成功,{os.path.abspath(output_path)}")

            except Exception as e:
                print(f"截圖失敗: {e}")
                error_output_path = os.path.join(screenshot_dir, f"error_{output_filename}")
                await page.screenshot(path=error_output_path, full_page=True)
                print(f"錯誤頁面截圖已保存到: {os.path.abspath(error_output_path)}")
            finally:
                await browser.close()
    except Exception as e:
        print(f"瀏覽器啟動失敗: {e}")
        print("請確保已安裝Playwright瀏覽器，執行: playwright install chromium")

async def main():
    """
    主函數，從CSV讀取IP並執行截圖
    """
    # 檢查並安裝Playwright瀏覽器
    install_playwright_browsers()

    if len(sys.argv) < 2:
        print("用法: python web_screenshot_whatis2_with_csv.py <csv_file> [screenshot_xpath] [wait_xpath]")
        print("範例: python web_screenshot_whatis2_with_csv.py ip_list.csv '//div[@class=\"ip-information\"]'")
        sys.exit(1)

    # 處理文件路徑，確保在VSCode中也能正確執行
    csv_file = os.path.abspath(sys.argv[1]) if not os.path.isabs(sys.argv[1]) else sys.argv[1]
    ip_addresses = get_ip_from_csv(csv_file)
    
    if not ip_addresses:
        print("未找到有效的IP地址，請檢查CSV文件格式")
        print("CSV文件要求:")
        print("1. 第一行為標題行")
        print("2. IP地址在第二列")
        print("3. IP地址格式為xxx.xxx.xxx.xxx")
        sys.exit(1)

    screenshot_xpath = sys.argv[2] if len(sys.argv) > 2 else '//div[@class="ip-information"]'
    wait_xpath = sys.argv[3] if len(sys.argv) > 3 else screenshot_xpath

    for ip in ip_addresses:
        print(f"正在處理IP: {ip}")
        url = f"https://whatismyipaddress.com/ip/{ip}"
        await capture_webpage_content(url, screenshot_xpath, wait_xpath)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"程式執行失敗: {e}")
        print("請確保已安裝所有依賴套件:")
        print("1. 執行: pip install -r requirements.txt")
        print("2. 執行: playwright install chromium")