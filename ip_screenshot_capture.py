import os
import time
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def take_screenshot(url, output_path):
    options = uc.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')

    driver = uc.Chrome(options=options)
    driver.get(url)

    try:
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
        time.sleep(5)  # 等待頁面加載完成
        driver.save_screenshot(output_path)
    except Exception as e:
        print(f"Error taking screenshot: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    ip_address = "*******"
    url = f"https://whatismyipaddress.com/ip/{ip_address}"
    output_path = f"{ip_address}_whatismyip.png"
    take_screenshot(url, output_path)
    print(f"Screenshot saved to {output_path}")
