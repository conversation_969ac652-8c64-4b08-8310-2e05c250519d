import webbrowser

def open_ip_pages(ip_address):
    """開啟IP查詢頁面"""
    urls = [
        f"https://whatismyipaddress.com/ip/{ip_address}",
        f"https://www.virustotal.com/gui/ip-address/{ip_address}"
    ]
    for url in urls:
        webbrowser.open(url)

if __name__ == "__main__":
    ip = input("請輸入IP地址(輸入q退出): ")
    if ip.lower() == 'q':
        exit()
    open_ip_pages(ip)
    print("已開啟查詢頁面，請手動查看結果")