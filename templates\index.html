<!DOCTYPE html>
<html>
<head>
    <title>IP查詢工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        input, button {
            padding: 10px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
    <script>
        // 阻止常見廣告域名的腳本
        const adDomains = ['adservice.google.com', 'pagead2.googlesyndication.com', 'ad.doubleclick.net'];
        adDomains.forEach(domain => {
            if (window.location.hostname.includes(domain)) {
                window.stop();
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>IP查詢工具</h1>
        <input type="text" id="ipInput" placeholder="輸入IP地址" value="***********">
        <button onclick="searchIP()">查詢IP</button>
    </div>

    <script>
        function searchIP() {
            const ip = document.getElementById('ipInput').value;
            fetch(`/search?ip=${ip}`)
                .then(response => {
                    if (response.ok) {
                        alert('已開啟IP查詢頁面');
                    } else {
                        alert('查詢失敗');
                    }
                });
        }
    </script>
</body>
</html>