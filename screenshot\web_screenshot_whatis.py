import asyncio
import sys
import os
import re
from urllib.parse import urlparse
from playwright.async_api import async_playwright, Page
import codecs # 引入 codecs 模組

# --- 新增：設定標準輸出為 UTF-8 編碼 ---
if sys.stdout.encoding != 'UTF-8':
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
# --- 結束新增 ---
async def capture_webpage_content(url: str, screenshot_xpath: str = None, wait_xpath: str = None):
    """
    導航到指定 URL，根據提供的 XPath 進行元素截圖，或截取整個頁面。
    可以指定一個獨立的 XPath 作為等待網頁內容載入的條件。
    截圖會存入 'screenshot/' 目錄，並根據網址命名。

    Args:
        url (str): 目標網址。
        screenshot_xpath (str, optional): 要截圖的元素 XPath。如果為 None，則截取整個頁面。
        wait_xpath (str, optional): 用於等待網頁載入完成的元素 XPath。
                                    如果為 None (且 screenshot_xpath 存在)，會使用 screenshot_xpath 作為等待條件。
                                    如果兩者皆為 None，則不會等待特定 XPath。
    """
    
    # 確保 screenshot/ 目錄存在
    screenshot_dir = ".\\screenshot"
    os.makedirs(screenshot_dir, exist_ok=True)

    # 根據是否有 screenshot_xpath 決定截圖檔名
    # 注意：這裡的判斷是基於是否有提供 screenshot_xpath，而不是舊的 'xpath' 變數
    if screenshot_xpath:
        parsed_url = urlparse(url)
        path_segments = parsed_url.path.split('/')
        
        if path_segments and path_segments[-1]:
            filename_base = path_segments[-1]
        else:
            filename_base = parsed_url.netloc.replace('.', '_').replace(':', '_')
            
        filename_base = re.sub(r'[^\w\d\-_.]', '', filename_base)
        if not filename_base:
            filename_base = "untitled_page"
            
        # 移除 .html 後綴，確保檔名簡潔
        output_filename = f"{filename_base}_element.png".replace('.html', '') 
    else:
        safe_url_name = re.sub(r'[^\w\d\-_.]', '_', url).replace('__', '_')
        # 移除 .html 後綴
        output_filename = f"{safe_url_name}_fullpage.png".replace('.html', '')
        
    output_path = os.path.join(screenshot_dir, output_filename)

    async with async_playwright() as p:
        # 啟動 Chromium 瀏覽器
        # 為了除錯或演示，您可以將 headless 設為 False 並加入 slow_mo 參數
        browser = await p.chromium.launch(headless=False, slow_mo=500) # 預設無頭模式
        page: Page = await browser.new_page()
        # 攔截圖片請求
        # 在導航到頁面之前，設置路由攔截
        await page.route("**/*", lambda route: route.abort() if route.request.resource_type == "image" else route.continue_())


        try:
            #print(f"導航到: {url}")
            await page.goto(url)

            # --- 等待邏輯 ---
            # 判斷要等待哪個 XPath：優先使用 wait_xpath，如果沒有則使用 screenshot_xpath
            actual_wait_xpath = wait_xpath if wait_xpath else screenshot_xpath

            if actual_wait_xpath:
                #print(f"等待 XPath 元素 '{actual_wait_xpath}' 出現並可見...")
                # Playwright 會自動判斷是 CSS 選擇器還是 XPath。為確保是 XPath，明確加上 "xpath=" 前綴                
                await page.wait_for_selector(f"xpath={actual_wait_xpath}", state="visible", timeout=30000) 
                #print(f"等待的元素 '{actual_wait_xpath}' 已出現。")
                #print(f"成功,{os.path.abspath(output_path)}")
            else:
                #print("未指定等待 XPath，將等待頁面載入完成。")
                # 如果沒有指定任何等待 XPath，可以選擇等待 networkidle，但不保證所有動態內容都渲染完成
                # 或者，如果頁面沒有動態內容，則可以省略額外的等待
                await page.wait_for_load_state("networkidle") # 等待網路閒置，常用於等待頁面靜態載入完成
                #print("頁面載入狀態為 networkidle。")
                #print(f"成功,{os.path.abspath(output_path)}")
            # --- 等待邏輯結束 ---
            
            await page.evaluate('''() => {
                    const element = document.evaluate('//*[@id="fs-sticky-footer"]', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (element) {
                        element.style.display = 'none';
                    }
                }''')
            
            if screenshot_xpath:
                #print(f"嘗試截圖指定 XPath 元素: {screenshot_xpath}")
                # 明確指定 XPath 選擇器來定位元素
                element_to_capture = page.locator(f"xpath={screenshot_xpath}") 
                await element_to_capture.screenshot(path=output_path)
                print(f"成功,{os.path.abspath(output_path)}")
            else:
                #print("未提供 screenshot_xpath，截取整個網頁畫面。")
                await page.screenshot(path=output_path, full_page=True) 
                print(f"成功,{os.path.abspath(output_path)}")

        except Exception as e:
            print(f"失敗: {e}")
            #print(f"錯誤訊息: {e}")
            # 如果截圖失敗，為了方便調試，可以選擇截取整個頁面，並加上 'error_' 前綴
            error_output_path = os.path.join(screenshot_dir, f"error_{output_filename}")
            await page.screenshot(path=error_output_path, full_page=True)
            #print(f"錯誤頁面截圖已保存到: {os.path.abspath(error_output_path)}")
        finally:
            await browser.close()
            #print("瀏覽器已關閉。")

if __name__ == "__main__":
    # 檢查命令行參數
    # sys.argv[0] 是腳本名稱
    # sys.argv[1] 是 URL
    # sys.argv[2] 是 screenshot_xpath
    # sys.argv[3] 是 wait_xpath (可選)
    if len(sys.argv) < 2: # 最少需要一個參數 (URL)
        #print("用法:")
        #print("  python your_script_name.py <url> [screenshot_xpath] [wait_xpath]")
        #print("\n範例:")
        #print("  # 截取整個網頁畫面")
        #print("  python your_script_name.py \"https://www.google.com\"")
        #print("  # 截圖指定元素，並使用該元素作為等待條件 (單一 XPath)")
        #print("  python your_script_name.py \"https://devmobile.entrust.com.tw/html/financial_chart.html\" '//*[@id=\"main-charts-wrapper\"]'")
        #print("  # 截圖指定元素，並使用另一個 XPath 作為等待條件 (兩個 XPath)")
        #print("  python your_script_name.py \"https://devmobile.entrust.com.tw/html/financial_chart.html\" '//*[@id=\"main-charts-wrapper\"]' '//*[@class=\"category-label\"]'")
        sys.exit(1) 

    target_url = sys.argv[1]
    target_screenshot_xpath = None 
    target_wait_xpath = None

    if len(sys.argv) > 2: # 如果提供了第二個參數 (screenshot_xpath)
        target_screenshot_xpath = sys.argv[2]
        if len(sys.argv) > 3: # 如果提供了第三個參數 (wait_xpath)
            target_wait_xpath = sys.argv[3]
        else: # 如果只提供了兩個參數，則將 screenshot_xpath 作為 wait_xpath
            target_wait_xpath = target_screenshot_xpath
    
    # 為了清晰起見，這裡可以列印出實際使用的參數
    #print(f"使用網址: {target_url}")
    #print(f"截圖 XPath: {target_screenshot_xpath if target_screenshot_xpath else '整個頁面'}")
    #print(f"等待 XPath: {target_wait_xpath if target_wait_xpath else '無指定等待 XPath'}")

    asyncio.run(capture_webpage_content(target_url, target_screenshot_xpath, target_wait_xpath))