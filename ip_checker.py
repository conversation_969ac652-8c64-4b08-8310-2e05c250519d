import asyncio
from playwright.async_api import async_playwright
import os
import re
import csv
import chardet
import logging
import argparse

# 配置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 解析命令行參數
parser = argparse.ArgumentParser(description='IP Checker Script')
parser.add_argument('--screenshot-ratio', type=float, default=1/3, help='The ratio of the page to screenshot (default: 1/3)')
parser.add_argument('--screenshot-x', type=int, default=0, help='The x-coordinate of the screenshot (default: 0)')
parser.add_argument('--screenshot-y', type=int, default=0, help='The y-coordinate of the screenshot (default: 0)')
parser.add_argument('--screenshot-width', type=int, default=1280, help='The width of the screenshot (default: 1280)')
parser.add_argument('--screenshot-height', type=int, default=0, help='The height of the screenshot (default: 0)')
parser.add_argument('--max-tabs', type=int, default=3, help='Maximum number of tabs to open simultaneously (default: 3)')
parser.add_argument('--delay-between-tabs', type=float, default=2.0, help='Delay in seconds between opening tabs (default: 2.0)')
parser.add_argument('--page-timeout', type=int, default=90000, help='Page load timeout in milliseconds (default: 90000)')
parser.add_argument('--max-retries', type=int, default=2, help='Maximum number of retries for failed requests (default: 2)')
args = parser.parse_args()

async def check_ip_single_site(page, ip_address, site_name, url, screenshot_dir, clip_config, wait_selector=None):
    """
    在單個分頁中查詢特定網站的IP地址並截圖。
    導航到指定 URL，根據提供的 XPath 進行元素截圖，或截取整個頁面。
    """
    try:
        logging.info(f"正在查詢 {site_name} - IP: {ip_address}")

        # 攔截圖片請求以加快載入速度
        await page.route("**/*", lambda route: route.abort() if route.request.resource_type == "image" else route.continue_())

        # 載入頁面：使用最激進的載入策略
        try:
            # 第一次嘗試：15秒超時，domcontentloaded
            await page.goto(url, wait_until="domcontentloaded", timeout=15000)
        except Exception as goto_error1:
            logging.warning(f"domcontentloaded 超時，嘗試 commit: {goto_error1}")
            try:
                # 第二次嘗試：15秒超時，commit
                await page.goto(url, wait_until="commit", timeout=15000)
            except Exception as goto_error2:
                logging.warning(f"commit 也超時，嘗試無等待: {goto_error2}")
                # 第三次嘗試：10秒超時，無等待條件
                await page.goto(url, timeout=10000)

        # 處理whatismyipaddress.com
        if "whatismyipaddress.com" in url:
            # 先等待頁面基本載入，使用短超時
            try:
                await page.wait_for_load_state("networkidle", timeout=10000)
            except Exception as networkidle_error:
                logging.warning(f"networkidle 等待超時，繼續執行: {networkidle_error}")
                # 即使 networkidle 失敗也繼續執行

            # 移除干擾元素和廣告
            await page.evaluate('''() => {
                // 移除 sticky footer
                const stickyFooter = document.evaluate('//*[@id="fs-sticky-footer"]', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (stickyFooter) {
                    stickyFooter.style.display = 'none';
                }

                // 移除各種廣告元素
                const adSelectors = [
                    '[id*="ad"]', '[class*="ad"]', '[id*="Ad"]', '[class*="Ad"]',
                    '[id*="banner"]', '[class*="banner"]',
                    '[id*="popup"]', '[class*="popup"]',
                    '[id*="overlay"]', '[class*="overlay"]',
                    '[class*="advertisement"]', '[id*="advertisement"]',
                    '[class*="promo"]', '[id*="promo"]',
                    'iframe[src*="ads"]', 'iframe[src*="doubleclick"]',
                    '[class*="sponsored"]', '[id*="sponsored"]',
                    '.google-auto-placed', '#google_ads_iframe',
                    '[data-ad-slot]', '[data-google-av-cxn]'
                ];

                adSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        el.style.display = 'none';
                        el.remove();
                    });
                });

                // 移除固定定位的元素（通常是廣告或彈窗）
                const fixedElements = document.querySelectorAll('*');
                fixedElements.forEach(el => {
                    const style = window.getComputedStyle(el);
                    if (style.position === 'fixed' || style.position === 'sticky') {
                        // 檢查是否可能是廣告
                        const rect = el.getBoundingClientRect();
                        if (rect.width > 100 && rect.height > 50) {
                            el.style.display = 'none';
                        }
                    }
                });
            }''')

            # 定義等待和截圖的 XPath（專門針對 ip-information）
            wait_xpaths = [
                "//div[@class='ip-information']",
                "//div[contains(@class, 'ip-information')]",
                "//div[@class='ip-detail expanded']",
                "//div[@class='ip-detail']",
                "//div[contains(@class, 'ip-detail')]",
                "//div[contains(@class, 'detail')]",
                "//div[contains(@class, 'ip')]"
            ]

            screenshot_path = f"{screenshot_dir}/{ip_address}_{site_name}.png"

            # 嘗試找到並截圖特定元素
            for xpath in wait_xpaths:
                try:
                    # 使用較短的超時時間等待元素可見
                    await page.wait_for_selector(f"xpath={xpath}", state="visible", timeout=10000)

                    # 截圖前再次清理廣告
                    await page.evaluate('''() => {
                        // 快速清理可能新出現的廣告
                        const quickAdSelectors = [
                            '[class*="ad"]', '[id*="ad"]',
                            '[class*="popup"]', '[class*="overlay"]',
                            '.google-auto-placed'
                        ];
                        quickAdSelectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => el.style.display = 'none');
                        });
                    }''')

                    # 截圖指定元素
                    element_to_capture = page.locator(f"xpath={xpath}")
                    await element_to_capture.screenshot(path=screenshot_path)
                    logging.info(f"XPath 截圖成功: {xpath}")
                    return True
                except Exception as xpath_error:
                    logging.warning(f"XPath {xpath} 失敗: {xpath_error}")
                    continue

            # 如果所有特定元素都找不到，返回失敗
            logging.error("所有 XPath 都失敗，無法找到目標元素")
            return False

        # 處理其他網站
        else:
            # 等待網路閒置，使用短超時
            try:
                await page.wait_for_load_state("networkidle", timeout=15000)
            except Exception as networkidle_error:
                logging.warning(f"其他網站 networkidle 等待超時，繼續執行: {networkidle_error}")

            viewport_height = await page.evaluate("document.documentElement.scrollHeight")
            target_height = int(viewport_height * args.screenshot_ratio)
            await page.set_viewport_size({"width": 1280, "height": target_height})

            screenshot_path = f"{screenshot_dir}/{ip_address}_{site_name}.png"
            await page.screenshot(path=screenshot_path, clip=clip_config)
            logging.info(f"截圖已保存: {screenshot_path}")
            return True

    except Exception as e:
        logging.error(f"查詢 {site_name} 失敗 (IP: {ip_address}): {e}")

        # 不進行錯誤截圖，只記錄錯誤
        logging.error(f"網站 {site_name} 處理失敗")

        return False

async def check_ip_sequential(ip_address, screenshot_dir, browser):
    """
    順序查詢IP地址並截圖：先完成 whatismyip，再執行 virustotal。

    參數:
    - ip_address: 要查詢的IP地址。
    - screenshot_dir: 截圖保存的目錄。
    - browser: Playwright瀏覽器實例。
    """
    # 定義要查詢的網站（按順序執行）
    sites = [
        {
            "name": "whatismyip",
            "url": f"https://whatismyipaddress.com/ip/{ip_address}",
            "clip": None,  # whatismyip 不使用 clip，直接截取元素
            "wait_selector": "div.ip-detail.expanded"  # 等待這個元素出現
        },
        {
            "name": "virustotal",
            "url": f"https://www.virustotal.com/gui/ip-address/{ip_address}",
            "clip": {
                "x": args.screenshot_x,
                "y": args.screenshot_y,
                "width": args.screenshot_width,
                "height": args.screenshot_height if args.screenshot_height > 0 else 400
            },
            "wait_selector": None  # virustotal 不需要等待特定元素
        }
    ]

    results = []

    try:
        # 順序執行每個網站
        for i, site in enumerate(sites):
            logging.info(f"開始處理 {site['name']} - IP: {ip_address}")

            page = await browser.new_page()



            try:
                # 執行單個網站的查詢和截圖
                result = await check_ip_single_site(
                    page, ip_address, site["name"],
                    site["url"], screenshot_dir, site["clip"],
                    site.get("wait_selector")
                )
                results.append(result)

                if result:
                    logging.info(f"{site['name']} 截圖完成 - IP: {ip_address}")
                else:
                    logging.error(f"{site['name']} 截圖失敗 - IP: {ip_address}")

                # 在處理下一個網站前添加短暫延遲
                if i < len(sites) - 1:
                    await asyncio.sleep(args.delay_between_tabs)

            except Exception as e:
                logging.error(f"處理 {site['name']} 時發生錯誤 (IP: {ip_address}): {e}")
                results.append(False)
            finally:
                # 關閉當前分頁
                try:
                    await page.close()
                except Exception as e:
                    logging.error(f"關閉分頁時發生錯誤: {e}")

        # 檢查結果
        success_count = sum(1 for result in results if result is True)
        logging.info(f"IP {ip_address} 完成，成功截圖 {success_count}/{len(sites)} 個網站")

    except Exception as e:
        logging.error(f"處理IP {ip_address} 時發生錯誤: {e}")

    return results

def detect_encoding(file_path):
    """
    檢測文件編碼。

    參數:
    - file_path: 要檢測編碼的文件路徑。

    返回值:
    - 文件的編碼。
    """
    with open(file_path, 'rb') as f:
        rawdata = f.read(10000)  # 讀取前10000字節來檢測編碼
        result = chardet.detect(rawdata)
        return result['encoding']

def get_ip_from_csv(file_path):
    """
    從CSV文件讀取IP地址。

    參數:
    - file_path: CSV文件的路徑。

    返回值:
    - 從CSV文件中讀取到的IP地址列表。
    """
    try:
        # 嘗試多種常見編碼
        encodings = ['utf-8-sig', 'big5', 'gbk', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    next(reader)  # 跳過標題行
                    ip_addresses = set()  # 使用集合去除重複
                    for row in reader:
                        if len(row) >= 2:  # 檢查是否有足夠的列
                            ip = row[1].strip()  # 假設IP在第二列
                            if re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', ip):
                                ip_addresses.add(ip)
                    if ip_addresses:  # 如果成功讀取到IP
                        return list(ip_addresses)
            except UnicodeDecodeError:
                continue
                
        logging.error("無法識別文件編碼")
        return None
    except Exception as e:
        logging.error(f"讀取CSV文件失敗: {e}")
        return None

async def process_ip_batch(ip_batch, screenshot_dir, browser):
    """
    批量處理IP地址，每個IP順序執行網站查詢。

    參數:
    - ip_batch: 要處理的IP地址列表。
    - screenshot_dir: 截圖保存的目錄。
    - browser: Playwright瀏覽器實例。
    """
    tasks = []
    for ip in ip_batch:
        task = check_ip_sequential(ip, screenshot_dir, browser)
        tasks.append(task)

    # 並行處理這批IP（每個IP內部是順序執行網站）
    await asyncio.gather(*tasks, return_exceptions=True)

async def main():
    """
    主函數，用於執行IP查詢和截圖。
    """
    files = ["re709.csv", "re821.csv", "re824.csv"]
    all_ips = []
    screenshot_dir = "screenshots"
    os.makedirs(screenshot_dir, exist_ok=True)

    for file in files:
        csv_file = os.path.join(os.path.expanduser("~"), "Desktop", "test", file)
        logging.info(f"正在嘗試從CSV文件讀取IP: {csv_file}")
        ip_addresses = get_ip_from_csv(csv_file)

        if ip_addresses:
            logging.info(f"成功讀取到 {len(ip_addresses)} 個IP地址")
            all_ips.extend(ip_addresses)

    if all_ips:
        logging.info(f"總共讀取到 {len(all_ips)} 個IP地址")
        logging.info(f"將使用最多 {args.max_tabs} 個同時分頁進行處理")

        async with async_playwright() as p:
            # 啟動瀏覽器
            browser = await p.chromium.launch(
                headless=False,
                slow_mo=500,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                ]
            )

            # 將IP地址分批處理，避免同時開啟太多分頁
            batch_size = args.max_tabs
            for i in range(0, len(all_ips), batch_size):
                batch = all_ips[i:i + batch_size]
                logging.info(f"正在處理第 {i//batch_size + 1} 批，共 {len(batch)} 個IP")
                await process_ip_batch(batch, screenshot_dir, browser)

                # 在批次之間添加短暫延遲
                if i + batch_size < len(all_ips):
                    await asyncio.sleep(2)

            await browser.close()
            logging.info("所有IP處理完成")
    else:
        logging.error("未找到有效的IP地址，請檢查CSV文件格式")
        print("CSV文件要求:")
        print("1. 第一行為標題行")
        print("2. IP地址在第二列")
        print("3. IP地址格式為xxx.xxx.xxx.xxx")

if __name__ == "__main__":
    asyncio.run(main())
