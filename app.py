from flask import Flask, render_template, request
import webbrowser
from threading import Timer

app = Flask(__name__)

def open_ip_page(ip_address):
    """Open browser tabs with IP information"""
    urls = [
        f"https://whatismyipaddress.com/ip/{ip_address}",
        f"https://www.virustotal.com/gui/ip-address/{ip_address}"
    ]
    for url in urls:
        webbrowser.open(url)

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/search')
def search():
    ip = request.args.get('ip', '***********')
    Timer(1, open_ip_page, args=[ip]).start()
    return '', 200

if __name__ == '__main__':
    app.run(debug=True)