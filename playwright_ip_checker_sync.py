from playwright.sync_api import sync_playwright
import os

def check_ip(ip_address):
    """使用 Playwright 同步API查詢IP並截圖"""
    screenshot_dir = "screenshots"
    os.makedirs(screenshot_dir, exist_ok=True)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # 查詢 whatismyip
            page.goto(f"https://whatismyip.com/ip/{ip_address}")
            page.screenshot(path=f"{screenshot_dir}/{ip_address}_whatismyip.png", full_page=True)
            
            # 查詢 virustotal
            page.goto(f"https://www.virustotal.com/gui/ip-address/{ip_address}")
            page.screenshot(path=f"{screenshot_dir}/{ip_address}_virustotal.png", full_page=True)
            
            print(f"查詢完成，截圖已儲存到 {screenshot_dir} 目錄")
        except Exception as e:
            print(f"查詢過程中發生錯誤: {e}")
        finally:
            browser.close()

if __name__ == "__main__":
    ip = input("請輸入要查詢的 IP 地址: ")
    check_ip(ip)