import requests
import socket

def get_public_ip():
    """取得公共IP位址"""
    try:
        response = requests.get('https://api.ipify.org?format=json')
        return response.json()['ip']
    except Exception as e:
        print(f"取得公共IP失敗: {e}")
        return None

def get_local_ip():
    """取得本地IP位址"""
    try:
        hostname = socket.gethostname()
        return socket.gethostbyname(hostname)
    except Exception as e:
        print(f"取得本地IP失敗: {e}")
        return None

if __name__ == "__main__":
    print("=== IP位址檢查工具 ===")
    public_ip = get_public_ip()
    local_ip = get_local_ip()
    
    if public_ip:
        print(f"您的公共IP位址是: {public_ip}")
    if local_ip:
        print(f"您的本地IP位址是: {local_ip}")