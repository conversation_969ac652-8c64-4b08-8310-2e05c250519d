import webbrowser
import os

def check_ip(ip_address):
    """開啟IP查詢頁面並提示截圖"""
    urls = [
        f"https://whatismyip.com/ip/{ip_address}",
        f"https://www.virustotal.com/gui/ip-address/{ip_address}"
    ]
    
    print("請使用以下方法截圖：")
    print("1. Windows: 按 Win+Shift+S 截取區域")
    print("2. Mac: 按 Command+Shift+4 截取區域")
    print("3. 截圖會自動儲存到桌面或剪貼簿")
    
    for url in urls:
        webbrowser.open(url)

if __name__ == "__main__":
    ip = input("請輸入要查詢的 IP 地址(輸入q退出): ")
    if ip.lower() == 'q':
        exit()
    check_ip(ip)
    print("請手動截圖保存查詢結果")