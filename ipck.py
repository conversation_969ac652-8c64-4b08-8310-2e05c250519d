import asyncio
from playwright.async_api import async_playwright
import os
import re
import csv
import chardet
import argparse
import logging

# 配置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def check_ip(ip_address):
    """使用Playwright查詢IP地址並截圖"""
    screenshot_dir = "screenshots"
    os.makedirs(screenshot_dir, exist_ok=True)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        
        try:
            # 查詢whatismyip
            await page.goto(f"https://whatismyipaddress.com/ip/{ip_address}")
            await page.wait_for_timeout(5000)
            await page.screenshot(path=f"{screenshot_dir}/{ip_address}_whatismyip.png", full_page=True)
            
            # 查詢virustotal
            await page.goto(f"https://www.virustotal.com/gui/ip-address/{ip_address}")
            await page.wait_for_timeout(5000)
            await page.screenshot(path=f"{screenshot_dir}/{ip_address}_virustotal.png", full_page=True)
            
            logging.info(f"截圖已保存到 {screenshot_dir} 目錄")
        except Exception as e:
            logging.error(f"查詢失敗: {e}")
        finally:
            await browser.close()

def detect_encoding(file_path):
    """檢測文件編碼"""
    with open(file_path, 'rb') as f:
        rawdata = f.read(10000)  # 讀取前10000字節來檢測編碼
        result = chardet.detect(rawdata)
        return result['encoding']

def get_ip_from_csv(file_path):
    """從CSV文件讀取IP地址"""
    try:
        # 嘗試多種常見編碼
        encodings = ['utf-8-sig', 'big5', 'gbk', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    next(reader)  # 跳過標題行
                    ip_addresses = set()  # 使用集合去除重複
                    for row in reader:
                        if len(row) >= 2:  # 檢查是否有足夠的列
                            ip = row[1].strip()  # 假設IP在第二列
                            if re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', ip):
                                ip_addresses.add(ip)
                    if ip_addresses:  # 如果成功讀取到IP
                        return list(ip_addresses)
            except UnicodeDecodeError:
                continue
                
        logging.error("無法識別文件編碼")
        return None
    except Exception as e:
        logging.error(f"讀取CSV文件失敗: {e}")
        return None

async def main():
    parser = argparse.ArgumentParser(description="查詢IP地址並截圖")
    parser.add_argument("files", nargs="+", help="CSV文件路徑")
    args = parser.parse_args()
    
    all_ips = []
    
    for file in args.files:
        csv_file = os.path.join(os.path.expanduser("~"), "Desktop", "test", file)
        logging.info(f"正在嘗試從CSV文件讀取IP: {csv_file}")
        ip_addresses = get_ip_from_csv(csv_file)
        
        if ip_addresses:
            logging.info(f"成功讀取到 {len(ip_addresses)} 個IP地址")
            all_ips.extend(ip_addresses)
    
    if all_ips:
        logging.info(f"總共讀取到 {len(all_ips)} 個IP地址")
        tasks = [check_ip(ip) for ip in all_ips]
        await asyncio.gather(*tasks)
    else:
        logging.error("未找到有效的IP地址，請檢查CSV文件格式")
        print("CSV文件要求:")
        print("1. 第一行為標題行")
        print("2. IP地址在第二列")
        print("3. IP地址格式為xxx.xxx.xxx.xxx")

if __name__ == "__main__":
    asyncio.run(main())
