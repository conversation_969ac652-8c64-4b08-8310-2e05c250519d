from playwright.sync_api import sync_playwright

def get_ip_info(ip_address):
    """使用Playwright查询IP信息"""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            page.goto(f"https://whatismyip.com/ip/{ip_address}")
            
            # 等待关键元素加载
            page.wait_for_selector(".ip-info")
            
            # 提取IP信息
            ip_info = page.text_content(".ip-info")
            print(f"IP信息: {ip_info}")
            
        except Exception as e:
            print(f"查询失败: {e}")
        finally:
            pass  # 不關閉瀏覽器

if __name__ == "__main__":
    ip = input("请输入要查询的IP地址: ")
    get_ip_info(ip)